const express = require('express');
const Joi = require('joi');
const { verifyToken } = require('./middleware/auth');
const { uploadSingleImage } = require('./middleware/upload');
const {
  addProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct
} = require('./controllers/productController');

const router = express.Router();

// Joi validation schemas
const colorSchema = Joi.object({
  color: Joi.string().trim().min(1).max(50).required().messages({
    'string.empty': 'Color name is required',
    'string.min': 'Color name must be at least 1 character',
    'string.max': 'Color name cannot exceed 50 characters'
  }),
  code: Joi.string().trim().max(20).optional().allow('', null).messages({
    'string.max': 'Color code cannot exceed 20 characters'
  }),
  quantity: Joi.number().integer().min(0).required().messages({
    'number.base': 'Quantity must be a number',
    'number.integer': 'Quantity must be an integer',
    'number.min': 'Quantity cannot be negative'
  })
});

const addProductSchema = Joi.object({
  productName: Joi.string().trim().min(1).max(200).required().messages({
    'string.empty': 'Product name is required',
    'string.min': 'Product name must be at least 1 character',
    'string.max': 'Product name cannot exceed 200 characters'
  }),
  code: Joi.string().trim().min(1).max(50).required().messages({
    'string.empty': 'Product code is required',
    'string.min': 'Product code must be at least 1 character',
    'string.max': 'Product code cannot exceed 50 characters'
  }),
  specificationAndPacking: Joi.string().trim().min(1).max(1000).required().messages({
    'string.empty': 'Specification and packing is required',
    'string.min': 'Specification and packing must be at least 1 character',
    'string.max': 'Specification and packing cannot exceed 1000 characters'
  }),
  colors: Joi.array().items(colorSchema).min(1).required().messages({
    'array.min': 'At least one color must be specified',
    'array.base': 'Colors must be an array'
  })
});

const updateProductSchema = Joi.object({
  productName: Joi.string().trim().min(1).max(200).optional().messages({
    'string.min': 'Product name must be at least 1 character',
    'string.max': 'Product name cannot exceed 200 characters'
  }),
  code: Joi.string().trim().min(1).max(50).optional().messages({
    'string.min': 'Product code must be at least 1 character',
    'string.max': 'Product code cannot exceed 50 characters'
  }),
  specificationAndPacking: Joi.string().trim().min(1).max(1000).optional().messages({
    'string.min': 'Specification and packing must be at least 1 character',
    'string.max': 'Specification and packing cannot exceed 1000 characters'
  }),
  colors: Joi.array().items(colorSchema).min(1).optional().messages({
    'array.min': 'At least one color must be specified',
    'array.base': 'Colors must be an array'
  })
});

const querySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Page must be a number',
    'number.integer': 'Page must be an integer',
    'number.min': 'Page must be at least 1'
  }),
  limit: Joi.number().integer().min(1).max(100).default(10).messages({
    'number.base': 'Limit must be a number',
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit cannot exceed 100'
  }),
  search: Joi.string().trim().max(100).optional().allow('').messages({
    'string.max': 'Search term cannot exceed 100 characters'
  })
});

const idSchema = Joi.object({
  id: Joi.string().hex().length(24).required().messages({
    'string.hex': 'Invalid product ID format',
    'string.length': 'Invalid product ID format'
  })
});

// Validation middleware
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }
    req.body = value;
    next();
  };
};

const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }
    req.query = value;
    next();
  };
};

const validateParams = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.params, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }
    req.params = value;
    next();
  };
};

// Health check route (no auth required)
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Apply authentication middleware to all API routes
router.use('/api', verifyToken);

// Product routes
router.post('/api/products',
  uploadSingleImage('productImage'),
  validateBody(addProductSchema),
  addProduct
);

router.get('/api/products',
  validateQuery(querySchema),
  getAllProducts
);

router.get('/api/products/:id',
  validateParams(idSchema),
  getProductById
);

router.put('/api/products/:id',
  validateParams(idSchema),
  uploadSingleImage('productImage'),
  validateBody(updateProductSchema),
  updateProduct
);

router.delete('/api/products/:id',
  validateParams(idSchema),
  deleteProduct
);

module.exports = router;
