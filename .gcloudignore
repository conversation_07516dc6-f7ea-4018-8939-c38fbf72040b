# This file specifies files that are intentionally untracked by gcloud
# and should not be uploaded to Google Cloud Platform

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Git files
.git/
.gitignore

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
README.md
*.md

# Test files
test/
tests/
__tests__/
*.test.js
*.spec.js

# Development files
.eslintrc*
.prettierrc*
jest.config.js
webpack.config.js

# Temporary files
tmp/
temp/

# Firebase
firebase-debug.log
.firebase/

# Deployment script (optional - remove this line if you want to include it)
deploy.sh
