const { auth } = require('../config/firebase');

/**
 * Middleware to verify Firebase authentication token
 * Checks if user is logged in by verifying the Firebase ID token
 * Accepts token from Authorization header, JSON body, or form data
 */
const verifyToken = async (req, res, next) => {
  try {
    let token = null;

    // Try to get token from Authorization header first (preferred method)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    }

    // If no token in header, try to get from request body (JSON or form data)
    if (!token && req.body && req.body.token) {
      token = req.body.token;
    }

    // If still no token, try to get from query parameters (fallback)
    if (!token && req.query && req.query.token) {
      token = req.query.token;
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'No authentication token provided. Please provide token in Authorization header, request body, or query parameters.'
      });
    }

    // Verify the Firebase ID token
    const decodedToken = await auth.verifyIdToken(token);
    
    // Add user information to request object
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      emailVerified: decodedToken.email_verified,
      name: decodedToken.name || null,
      picture: decodedToken.picture || null
    };

    // Remove token from request body to prevent it from being passed to controllers
    if (req.body && req.body.token) {
      delete req.body.token;
    }

    next();
  } catch (error) {
    console.error('Token verification error:', error);
    
    // Handle specific Firebase auth errors
    if (error.code === 'auth/id-token-expired') {
      return res.status(401).json({
        success: false,
        message: 'Token has expired'
      });
    }
    
    if (error.code === 'auth/id-token-revoked') {
      return res.status(401).json({
        success: false,
        message: 'Token has been revoked'
      });
    }
    
    if (error.code === 'auth/invalid-id-token') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }

    return res.status(401).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

module.exports = { verifyToken };
