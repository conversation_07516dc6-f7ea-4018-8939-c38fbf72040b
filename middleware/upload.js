const multer = require('multer');
const { bucket } = require('../config/firebase');
const path = require('path');

// Configure multer for memory storage
const storage = multer.memoryStorage();

// File filter to only allow images
const fileFilter = (req, file, cb) => {
  // Check if file is an image
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

// Multer configuration
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 5MB limit
  }
});

/**
 * Upload file to Firebase Storage and get signed URL
 * @param {Buffer} fileBuffer - File buffer from multer
 * @param {string} fileName - Original filename
 * @param {string} mimetype - File mimetype
 * @returns {Promise<string>} - Signed URL for the uploaded file
 */
const uploadToFirebase = async (fileBuffer, fileName, mimetype) => {
  try {
    // Generate unique filename
    const timestamp = Date.now();
    const extension = path.extname(fileName);
    const uniqueFileName = `products/${timestamp}_${Math.random().toString(36).substring(2)}${extension}`;

    // Create file reference in Firebase Storage
    const file = bucket.file(uniqueFileName);

    // Upload file buffer to Firebase Storage
    await file.save(fileBuffer, {
      metadata: {
        contentType: mimetype,
      },
      public: false, // Keep file private, access via signed URL
    });

    // Generate signed URL (valid for 1 year)
    const [signedUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + 365 * 24 * 60 * 60 * 1000 * 99, // 99 year from now
    });

    return signedUrl;
  } catch (error) {
    console.error('Error uploading to Firebase:', error);
    throw new Error('Failed to upload file to storage');
  }
};

/**
 * Middleware to handle single file upload and upload to Firebase
 */
const uploadSingleImage = (fieldName) => {
  return async (req, res, next) => {
    // Use multer to handle the file upload
    const multerUpload = upload.single(fieldName);

    multerUpload(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              message: 'File size too large. Maximum size is 5MB.'
            });
          }
        }
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }

      // If no file uploaded, continue to next middleware
      if (!req.file) {
        return next();
      }

      try {
        // Upload to Firebase and get signed URL
        const signedUrl = await uploadToFirebase(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype
        );

        // Add the signed URL to request object
        req.uploadedImageUrl = signedUrl;
        next();
      } catch (error) {
        return res.status(500).json({
          success: false,
          message: 'Failed to upload image'
        });
      }
    });
  };
};

module.exports = {
  uploadSingleImage,
  uploadToFirebase
};
