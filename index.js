const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Import configurations
const connectDB = require('./config/database');

// Import routes
const routes = require('./controllers/routes');

// Initialize Express app
const app = express();

// Connect to MongoDB
connectDB();

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || '*',
  credentials: true
}));

// Parse JSON payloads
app.use(express.json({
  limit: '10mb',
  type: ['application/json', 'text/plain']
}));

// Parse URL-encoded payloads (form data)
app.use(express.urlencoded({
  extended: true,
  limit: '10mb',
  type: 'application/x-www-form-urlencoded'
}));

// Parse multipart/form-data is handled by multer middleware in routes

// Root route
app.get('/', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Will Global Trading Backend API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      products: '/api/products'
    }
  });
});

// All routes (including health check and API routes)
app.use('/', routes);

// 404 handler
// app.use('*', (req, res) => {
//   res.status(404).json({
//     success: false,
//     message: 'Route not found'
//   });
// });

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);

  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📱 Health check: http://localhost:${PORT}/health`);
  console.log(`📦 Products API: http://localhost:${PORT}/api/products`);
});
