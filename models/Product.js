const mongoose = require('mongoose');

// Color schema for the colors array
const colorSchema = new mongoose.Schema({
  color: {
    type: String,
    required: true,
    trim: true
  },
  code: {
    type: String,
    trim: true,
    default: null
  },
  quantity: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  }
}, { _id: false });

// Product schema
const productSchema = new mongoose.Schema({
  productName: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [200, 'Product name cannot exceed 200 characters']
  },
  code: {
    type: String,
    required: [true, 'Product code is required'],
    trim: true,
    unique: true,
    maxlength: [50, 'Product code cannot exceed 50 characters']
  },
  specificationAndPacking: {
    type: String,
    required: [true, 'Specification and packing is required'],
    trim: true,
    maxlength: [1000, 'Specification and packing cannot exceed 1000 characters']
  },
  productImageUrl: {
    type: String,
    required: [true, 'Product image URL is required'],
    trim: true
  },
  colors: {
    type: [colorSchema],
    required: true,
    validate: {
      validator: function(colors) {
        return colors && colors.length > 0;
      },
      message: 'At least one color must be specified'
    }
  },
  totalQuantity: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  createdBy: {
    type: String,
    required: true
  },
  updatedBy: {
    type: String,
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Pre-save middleware to calculate totalQuantity
productSchema.pre('save', function(next) {
  if (this.colors && this.colors.length > 0) {
    this.totalQuantity = this.colors.reduce((total, color) => {
      return total + (color.quantity || 0);
    }, 0);
  } else {
    this.totalQuantity = 0;
  }
  next();
});

// Pre-update middleware to calculate totalQuantity
productSchema.pre(['findOneAndUpdate', 'updateOne', 'updateMany'], function(next) {
  const update = this.getUpdate();
  if (update.colors) {
    const totalQuantity = update.colors.reduce((total, color) => {
      return total + (color.quantity || 0);
    }, 0);
    update.totalQuantity = totalQuantity;
  }
  next();
});

// Index for better query performance
productSchema.index({ code: 1 });
productSchema.index({ productName: 1 });
productSchema.index({ createdAt: -1 });

const Product = mongoose.model('Product', productSchema);

module.exports = Product;
