const mongoose = require('mongoose');

// Order item schema for the items array
const orderItemSchema = new mongoose.Schema({
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  productName: {
    type: String,
    required: true
  },
  productCode: {
    type: String,
    required: true
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  colorCode: {
    type: String,
    trim: true,
    default: null
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  // unitPrice: {
  //   type: Number,
  //   required: true,
  //   min: 0
  // },
  // totalPrice: {
  //   type: Number,
  //   required: true,
  //   min: 0
  // }
}, { _id: false });

// Order schema
const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    // required: true,
    unique: true,
    trim: true
  },
  customerName: {
    type: String,
    required: [true, 'Customer name is required'],
    trim: true,
    maxlength: [100, 'Customer name cannot exceed 100 characters']
  },
  customerPhone: {
    type: String,
    required: [true, 'Customer phone is required'],
    trim: true,
    maxlength: [20, 'Customer phone cannot exceed 20 characters']
  },
  items: {
    type: [orderItemSchema],
    required: true,
    validate: {
      validator: function(items) {
        return items && items.length > 0;
      },
      message: 'At least one item must be specified'
    }
  },
  // totalAmount: {
  //   type: Number,
  //   required: true,
  //   min: 0
  // },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'],
    default: 'pending'
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot exceed 1000 characters'],
    default: ''
  },
  createdBy: {
    type: String,
    required: true
  },
  updatedBy: {
    type: String,
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Pre-save middleware to calculate total amount
// orderSchema.pre('save', function(next) {
//   if (this.items && this.items.length > 0) {
//     this.totalAmount = this.items.reduce((total, item) => {
//       return total + (item.totalPrice || 0);
//     }, 0);
//   } else {
//     this.totalAmount = 0;
//   }
//   next();
// });

// Pre-save middleware to generate order number if not provided
orderSchema.pre('save', function(next) {
  if (!this.orderNumber) {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    this.orderNumber = `ORD-${timestamp}-${random}`;
  }
  next();
});

// Index for better query performance
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ customerEmail: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ createdAt: -1 });

const Order = mongoose.model('Order', orderSchema);

module.exports = Order;
