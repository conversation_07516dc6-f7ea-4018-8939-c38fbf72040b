{"name": "will-global-trading-backend", "version": "1.0.0", "description": "Express.js backend for Will Global Trading with Firebase auth and MongoDB", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "private": true, "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "firebase-admin": "^13.5.0", "mongoose": "^8.18.0", "multer": "^2.0.2"}, "devDependencies": {"nodemon": "^3.1.10"}}