# Will Global Trading Backend

Express.js backend API for Will Global Trading with Firebase authentication, MongoDB database, and Firebase Storage for file uploads.

## Features

- **Authentication**: Firebase Authentication with JWT token verification
- **Database**: MongoDB with Mongoose ODM
- **File Upload**: Firebase Storage integration with signed URLs
- **Product Management**: Full CRUD operations for products
- **Security**: Authentication middleware on all routes
- **Error Handling**: Comprehensive error handling and validation

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose
- **Authentication**: Firebase Admin SDK
- **File Storage**: Firebase Storage
- **File Upload**: Multer
- **Validation**: Joi for request validation
- **Environment**: dotenv

## Project Structure

```
will-global-trading-backend/
├── config/
│   ├── database.js          # MongoDB connection
│   └── firebase.js          # Firebase configuration
├── controllers/
│   └── productController.js # Product CRUD operations
├── dto/
│   ├── productDto.js        # Product validation schemas
│   ├── commonDto.js         # Common validation schemas
│   └── index.js             # DTO exports
├── middleware/
│   ├── auth.js              # Authentication middleware
│   └── upload.js            # File upload middleware
├── models/
│   └── Product.js           # Product schema
├── routes.js                # All API routes with Joi validation
├── .env.example             # Environment variables template
├── index.js                 # Main server file
└── package.json
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env .env
   ```

4. Configure your environment variables in `.env`

## Environment Variables

Create a `.env` file with the following variables:

```env
# Server Configuration
PORT=3000

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/will-global-trading

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=your-client-email
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_STORAGE_BUCKET=your-storage-bucket.appspot.com

# Environment
NODE_ENV=development
```

## API Endpoints

### Authentication
All routes require Firebase authentication token in the Authorization header:
```
Authorization: Bearer <firebase-id-token>
```

### Products

| Method | Endpoint | Description | Body/Query |
|--------|----------|-------------|------------|
| POST | `/api/products` | Add new product | Form data with image |
| GET | `/api/products` | Get all products | Query: page, limit, search |
| GET | `/api/products/:id` | Get product by ID | - |
| PUT | `/api/products/:id` | Update product | Form data with optional image |
| DELETE | `/api/products/:id` | Delete product | - |

### Product Schema

```javascript
{
  productName: String (required),
  code: String (required, unique),
  specificationAndPacking: String (required),
  productImageUrl: String (required),
  colors: [
    {
      color: String (required),
      code: String (optional),
      quantity: Number (required, min: 0)
    }
  ],
  totalQuantity: Number (auto-calculated),
  createdBy: String (user ID),
  updatedBy: String (user ID),
  createdAt: Date,
  updatedAt: Date
}
```

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Health Check
```bash
curl http://localhost:3000/health
```

## File Upload

- **Field name**: `productImage`
- **Accepted formats**: Images only (jpg, png, gif, etc.)
- **Max size**: 5MB
- **Storage**: Firebase Storage with signed URLs
- **URL validity**: 1 year

## Validation

All incoming data (except files) is validated using Joi schemas organized in the `dto/` folder:

### DTO Structure
- **`dto/productDto.js`**: Product-specific validation schemas
- **`dto/commonDto.js`**: Shared validation schemas (pagination, IDs)
- **`dto/index.js`**: Centralized exports for all DTOs

### Validation Rules
- **Product Name**: 1-200 characters, required
- **Code**: 1-50 characters, required, unique
- **Specification**: 1-1000 characters, required
- **Colors**: Array of objects, minimum 1 item
  - **Color**: 1-50 characters, required
  - **Code**: 0-20 characters, optional
  - **Quantity**: Non-negative integer, required
- **Query Parameters**: Page (min 1), Limit (1-100), Search (max 100 chars)
- **MongoDB IDs**: 24-character hexadecimal format

## Error Handling

The API returns consistent error responses:

```javascript
{
  "success": false,
  "message": "Error description",
  "errors": [] // Validation errors if applicable
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request
