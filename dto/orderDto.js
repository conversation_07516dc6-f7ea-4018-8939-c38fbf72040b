const Joi = require('joi');

// Order item schema for the items array
const orderItemSchema = Joi.object({
  productId: Joi.string().hex().length(24).required().messages({
    'string.hex': 'Invalid product ID format',
    'string.length': 'Invalid product ID format',
    'any.required': 'Product ID is required'
  }),
  color: Joi.string().trim().min(1).max(50).required().messages({
    'string.empty': 'Color is required',
    'string.min': 'Color must be at least 1 character',
    'string.max': 'Color cannot exceed 50 characters',
    'any.required': 'Color is required'
  }),
  quantity: Joi.number().integer().min(1).required().messages({
    'number.base': 'Quantity must be a number',
    'number.integer': 'Quantity must be an integer',
    'number.min': 'Quantity must be at least 1',
    'any.required': 'Quantity is required'
  }),
  // unitPrice: Joi.number().min(0).required().messages({
  //   'number.base': 'Unit price must be a number',
  //   'number.min': 'Unit price cannot be negative',
  //   'any.required': 'Unit price is required'
  // })
});

// Schema for adding a new order
const addOrderSchema = Joi.object({
  customerName: Joi.string().trim().min(1).max(100).required().messages({
    'string.empty': 'Customer name is required',
    'string.min': 'Customer name must be at least 1 character',
    'string.max': 'Customer name cannot exceed 100 characters',
    'any.required': 'Customer name is required'
  }),
  customerPhone: Joi.string().trim().min(1).max(20).required().messages({
    'string.empty': 'Customer phone is required',
    'string.min': 'Customer phone must be at least 1 character',
    'string.max': 'Customer phone cannot exceed 20 characters',
    'any.required': 'Customer phone is required'
  }),
  items: Joi.array().items(orderItemSchema).min(1).required().messages({
    'array.min': 'At least one item must be specified',
    'array.base': 'Items must be an array',
    'any.required': 'Items are required'
  }),
  notes: Joi.string().trim().max(1000).optional().allow('').messages({
    'string.max': 'Notes cannot exceed 1000 characters'
  }),
  // Optional token field for authentication (will be stripped after validation)
  token: Joi.string().optional().messages({
    'string.base': 'Token must be a string'
  })
});

// Schema for updating an order
const updateOrderSchema = Joi.object({
  customerName: Joi.string().trim().min(1).max(100).optional().messages({
    'string.min': 'Customer name must be at least 1 character',
    'string.max': 'Customer name cannot exceed 100 characters'
  }),
  customerPhone: Joi.string().trim().min(1).max(20).optional().messages({
    'string.min': 'Customer phone must be at least 1 character',
    'string.max': 'Customer phone cannot exceed 20 characters'
  }),
  items: Joi.array().items(orderItemSchema).min(1).optional().messages({
    'array.min': 'At least one item must be specified',
    'array.base': 'Items must be an array'
  }),
  status: Joi.string().valid('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled').optional().messages({
    'any.only': 'Status must be one of: pending, confirmed, processing, shipped, delivered, cancelled'
  }),
  notes: Joi.string().trim().max(1000).optional().allow('').messages({
    'string.max': 'Notes cannot exceed 1000 characters'
  }),
  // Optional token field for authentication (will be stripped after validation)
  token: Joi.string().optional().messages({
    'string.base': 'Token must be a string'
  })
});

// Schema for order query parameters
const orderQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Page must be a number',
    'number.integer': 'Page must be an integer',
    'number.min': 'Page must be at least 1'
  }),
  limit: Joi.number().integer().min(1).max(100).default(10).messages({
    'number.base': 'Limit must be a number',
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit cannot exceed 100'
  }),
  search: Joi.string().trim().max(100).optional().allow('').messages({
    'string.max': 'Search term cannot exceed 100 characters'
  }),
  status: Joi.string().valid('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled').optional().messages({
    'any.only': 'Status must be one of: pending, confirmed, processing, shipped, delivered, cancelled'
  })
});

module.exports = {
  orderItemSchema,
  addOrderSchema,
  updateOrderSchema,
  orderQuerySchema
};
