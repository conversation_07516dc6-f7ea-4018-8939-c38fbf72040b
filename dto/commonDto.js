const Joi = require('joi');

// Schema for query parameters (pagination and search)
const querySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Page must be a number',
    'number.integer': 'Page must be an integer',
    'number.min': 'Page must be at least 1'
  }),
  limit: Joi.number().integer().min(1).max(100).default(10).messages({
    'number.base': 'Limit must be a number',
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit cannot exceed 100'
  }),
  search: Joi.string().trim().max(100).optional().allow('').messages({
    'string.max': 'Search term cannot exceed 100 characters'
  })
});

// Schema for MongoDB ObjectId validation
const idSchema = Joi.object({
  id: Joi.string().hex().length(24).required().messages({
    'string.hex': 'Invalid ID format',
    'string.length': 'Invalid ID format'
  })
});

// Schema for general string ID validation (can be used for other ID types)
const stringIdSchema = Joi.object({
  id: Joi.string().trim().min(1).required().messages({
    'string.empty': 'ID is required',
    'string.min': 'ID must be at least 1 character'
  })
});

module.exports = {
  querySchema,
  idSchema,
  stringIdSchema
};
