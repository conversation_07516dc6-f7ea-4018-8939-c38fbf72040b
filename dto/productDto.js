const Joi = require('joi');

// Color schema for the colors array
const colorSchema = Joi.object({
  color: Joi.string().trim().min(1).max(50).required().messages({
    'string.empty': 'Color name is required',
    'string.min': 'Color name must be at least 1 character',
    'string.max': 'Color name cannot exceed 50 characters'
  }),
  code: Joi.string().trim().max(20).optional().allow('', null).messages({
    'string.max': 'Color code cannot exceed 20 characters'
  }),
  quantity: Joi.number().integer().min(0).required().messages({
    'number.base': 'Quantity must be a number',
    'number.integer': 'Quantity must be an integer',
    'number.min': 'Quantity cannot be negative'
  })
});

// Schema for adding a new product
const addProductSchema = Joi.object({
  productName: Joi.string().trim().min(1).max(200).required().messages({
    'string.empty': 'Product name is required',
    'string.min': 'Product name must be at least 1 character',
    'string.max': 'Product name cannot exceed 200 characters'
  }),
  code: Joi.string().trim().min(1).max(50).required().messages({
    'string.empty': 'Product code is required',
    'string.min': 'Product code must be at least 1 character',
    'string.max': 'Product code cannot exceed 50 characters'
  }),
  specificationAndPacking: Joi.string().trim().min(1).max(1000).required().messages({
    'string.empty': 'Specification and packing is required',
    'string.min': 'Specification and packing must be at least 1 character',
    'string.max': 'Specification and packing cannot exceed 1000 characters'
  }),
  colors: Joi.array().items(colorSchema).min(1).required().messages({
    'array.min': 'At least one color must be specified',
    'array.base': 'Colors must be an array'
  })
});

// Schema for updating a product
const updateProductSchema = Joi.object({
  productName: Joi.string().trim().min(1).max(200).optional().messages({
    'string.min': 'Product name must be at least 1 character',
    'string.max': 'Product name cannot exceed 200 characters'
  }),
  code: Joi.string().trim().min(1).max(50).optional().messages({
    'string.min': 'Product code must be at least 1 character',
    'string.max': 'Product code cannot exceed 50 characters'
  }),
  specificationAndPacking: Joi.string().trim().min(1).max(1000).optional().messages({
    'string.min': 'Specification and packing must be at least 1 character',
    'string.max': 'Specification and packing cannot exceed 1000 characters'
  }),
  colors: Joi.array().items(colorSchema).min(1).optional().messages({
    'array.min': 'At least one color must be specified',
    'array.base': 'Colors must be an array'
  })
});

module.exports = {
  colorSchema,
  addProductSchema,
  updateProductSchema
};
