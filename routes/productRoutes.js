const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth');
const { uploadSingleImage } = require('../middleware/upload');
const {
  addProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct
} = require('../controllers/productController');

// Apply authentication middleware to all routes
router.use(verifyToken);

/**
 * @route   POST /api/products
 * @desc    Add a new product
 * @access  Private (requires authentication)
 * @body    productName, code, specificationAndPacking, colors
 * @file    productImage (required)
 */
router.post('/', uploadSingleImage('productImage'), addProduct);

/**
 * @route   GET /api/products
 * @desc    Get all products with pagination and search
 * @access  Private (requires authentication)
 * @query   page, limit, search
 */
router.get('/', getAllProducts);

/**
 * @route   GET /api/products/:id
 * @desc    Get a single product by ID
 * @access  Private (requires authentication)
 */
router.get('/:id', getProductById);

/**
 * @route   PUT /api/products/:id
 * @desc    Update a product
 * @access  Private (requires authentication)
 * @body    productName, code, specificationAndPacking, colors
 * @file    productImage (optional)
 */
router.put('/:id', uploadSingleImage('productImage'), updateProduct);

/**
 * @route   DELETE /api/products/:id
 * @desc    Delete a product
 * @access  Private (requires authentication)
 */
router.delete('/:id', deleteProduct);

module.exports = router;
