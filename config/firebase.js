const admin = require('firebase-admin');
require('dotenv').config();

// Firebase Admin SDK configuration
const serviceAccount = require("../will-global-trading.json");

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET
  });
}

// Get Firebase services
const auth = admin.auth();
const storage = admin.storage();
const bucket = storage.bucket();

module.exports = {
  admin,
  auth,
  storage,
  bucket
};
