const mongoose = require('mongoose');
const Order = require('../models/Order');
const Product = require('../models/Product');
const {
  addOrderSchema,
  updateOrderSchema,
  orderQuerySchema,
  idSchema
} = require('../dto');

/**
 * Add a new order
 * POST /api/orders
 */
const addOrder = async (req, res) => {
  try {
    // Validate request body
    const { error, value } = addOrderSchema.validate(req.body, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { customerName, customerEmail, customerPhone, customerAddress, items, notes } = value;

    // Start a session for transaction
    const session = await mongoose.startSession();

    try {
      // Start transaction
      await session.startTransaction();

      // Verify products and colors exist, and check stock availability
      const verifiedItems = [];
      for (const item of items) {
        const product = await Product.findById(item.productId).session(session);
        if (!product) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message: `Product with ID ${item.productId} not found`
          });
        }

        // Find the specific color in the product
        const colorMatch = product.colors.find(c => c.color.toLowerCase() === item.color.toLowerCase());
        if (!colorMatch) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message: `Color '${item.color}' not available for product '${product.productName}'`
          });
        }

        // Check if enough stock is available
        if (colorMatch.quantity < item.quantity) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message: `Insufficient stock for product '${product.productName}' in color '${item.color}'. Available: ${colorMatch.quantity}, Requested: ${item.quantity}`
          });
        }

        // Calculate total price for this item
        const totalPrice = item.unitPrice * item.quantity;

        verifiedItems.push({
          productId: product._id,
          productName: product.productName,
          productCode: product.code,
          color: colorMatch.color,
          colorCode: colorMatch.code,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: totalPrice
        });
      }

      // Create the order within transaction
      const newOrder = new Order({
        customerName,
        customerEmail,
        customerPhone,
        customerAddress,
        items: verifiedItems,
        notes: notes || '',
        createdBy: req.user.uid,
        updatedBy: req.user.uid
      });

      const savedOrder = await newOrder.save({ session });

      // Deduct quantities from products within transaction
      for (const item of verifiedItems) {
        const updateResult = await Product.findOneAndUpdate(
          {
            _id: item.productId,
            'colors.color': item.color,
            'colors.quantity': { $gte: item.quantity } // Ensure quantity is still available
          },
          {
            $inc: { 'colors.$.quantity': -item.quantity },
            updatedBy: req.user.uid
          },
          { session, new: true }
        );

        // Double-check that the update was successful
        if (!updateResult) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message: `Failed to update stock for product '${item.productName}' in color '${item.color}'. Stock may have been modified by another transaction.`
          });
        }
      }

      // Commit the transaction
      await session.commitTransaction();

      // Populate the saved order with product details (outside transaction)
      const populatedOrder = await Order.findById(savedOrder._id).populate('items.productId');

      res.status(201).json({
        success: true,
        message: 'Order created successfully',
        data: populatedOrder
      });

    } catch (transactionError) {
      // Abort transaction on any error
      await session.abortTransaction();
      throw transactionError;
    } finally {
      // End session
      await session.endSession();
    }

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: populatedOrder
    });
  } catch (error) {
    console.error('Error adding order:', error);
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: Object.values(error.errors).map(err => err.message)
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get all orders
 * GET /api/orders
 */
const getAllOrders = async (req, res) => {
  try {
    // Validate query parameters
    const { error, value } = orderQuerySchema.validate(req.query, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { page, limit, search, status } = value;
    
    // Build search query
    let searchQuery = {};
    
    if (search) {
      searchQuery.$or = [
        { orderNumber: { $regex: search, $options: 'i' } },
        { customerName: { $regex: search, $options: 'i' } },
        { customerEmail: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      searchQuery.status = status;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get orders with pagination
    const orders = await Order.find(searchQuery)
      .populate('items.productId', 'productName code')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalOrders = await Order.countDocuments(searchQuery);
    const totalPages = Math.ceil(totalOrders / limit);

    res.status(200).json({
      success: true,
      data: orders,
      pagination: {
        currentPage: page,
        totalPages,
        totalOrders,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Error getting orders:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get a single order by ID
 * GET /api/orders/:id
 */
const getOrderById = async (req, res) => {
  try {
    // Validate ID parameter
    const { error, value } = idSchema.validate(req.params, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { id } = value;

    const order = await Order.findById(id).populate('items.productId', 'productName code specificationAndPacking');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.status(200).json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Error getting order:', error);
    
    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid order ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Update an order
 * PUT /api/orders/:id
 */
const updateOrder = async (req, res) => {
  try {
    // Validate ID parameter
    const { error: paramError, value: paramValue } = idSchema.validate(req.params, { abortEarly: false });
    if (paramError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: paramError.details.map(detail => detail.message)
      });
    }

    // Validate request body
    const { error: bodyError, value: bodyValue } = updateOrderSchema.validate(req.body, { abortEarly: false });
    if (bodyError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: bodyError.details.map(detail => detail.message)
      });
    }

    const { id } = paramValue;
    const { customerName, customerEmail, customerPhone, customerAddress, items, status, notes } = bodyValue;

    // Check if order exists
    const existingOrder = await Order.findById(id);
    if (!existingOrder) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // If items are being updated, we need to handle stock adjustments with transaction
    let verifiedItems;
    if (items) {
      // Start a session for transaction
      const session = await mongoose.startSession();

      try {
        // Start transaction
        await session.startTransaction();

        // First, restore the quantities from the original order
        for (const originalItem of existingOrder.items) {
          await Product.findOneAndUpdate(
            {
              _id: originalItem.productId,
              'colors.color': originalItem.color
            },
            {
              $inc: { 'colors.$.quantity': originalItem.quantity },
              updatedBy: req.user.uid
            },
            { session }
          );
        }

        // Verify new products and colors exist, and check stock availability
        verifiedItems = [];
        for (const item of items) {
          const product = await Product.findById(item.productId).session(session);
          if (!product) {
            await session.abortTransaction();
            return res.status(400).json({
              success: false,
              message: `Product with ID ${item.productId} not found`
            });
          }

          // Find the specific color in the product
          const colorMatch = product.colors.find(c => c.color.toLowerCase() === item.color.toLowerCase());
          if (!colorMatch) {
            await session.abortTransaction();
            return res.status(400).json({
              success: false,
              message: `Color '${item.color}' not available for product '${product.productName}'`
            });
          }

          // Check if enough stock is available (after restoration)
          if (colorMatch.quantity < item.quantity) {
            await session.abortTransaction();
            return res.status(400).json({
              success: false,
              message: `Insufficient stock for product '${product.productName}' in color '${item.color}'. Available: ${colorMatch.quantity}, Requested: ${item.quantity}`
            });
          }

          // Calculate total price for this item
          const totalPrice = item.unitPrice * item.quantity;

          verifiedItems.push({
            productId: product._id,
            productName: product.productName,
            productCode: product.code,
            color: colorMatch.color,
            colorCode: colorMatch.code,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: totalPrice
          });
        }

        // Deduct quantities for new items within transaction
        for (const item of verifiedItems) {
          const updateResult = await Product.findOneAndUpdate(
            {
              _id: item.productId,
              'colors.color': item.color,
              'colors.quantity': { $gte: item.quantity } // Ensure quantity is still available
            },
            {
              $inc: { 'colors.$.quantity': -item.quantity },
              updatedBy: req.user.uid
            },
            { session, new: true }
          );

          // Double-check that the update was successful
          if (!updateResult) {
            await session.abortTransaction();
            return res.status(400).json({
              success: false,
              message: `Failed to update stock for product '${item.productName}' in color '${item.color}'. Stock may have been modified by another transaction.`
            });
          }
        }

        // Commit the transaction
        await session.commitTransaction();

      } catch (transactionError) {
        // Abort transaction on any error
        await session.abortTransaction();
        throw transactionError;
      } finally {
        // End session
        await session.endSession();
      }
    }

    // Prepare update object
    const updateData = {
      updatedBy: req.user.uid
    };

    if (customerName) updateData.customerName = customerName;
    if (customerEmail) updateData.customerEmail = customerEmail;
    if (customerPhone) updateData.customerPhone = customerPhone;
    if (customerAddress) updateData.customerAddress = customerAddress;
    if (items) updateData.items = verifiedItems;
    if (status) updateData.status = status;
    if (notes !== undefined) updateData.notes = notes;

    const updatedOrder = await Order.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('items.productId', 'productName code specificationAndPacking');

    res.status(200).json({
      success: true,
      message: 'Order updated successfully',
      data: updatedOrder
    });
  } catch (error) {
    console.error('Error updating order:', error);

    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: Object.values(error.errors).map(err => err.message)
      });
    }

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid order ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  addOrder,
  getAllOrders,
  getOrderById,
  updateOrder
};
