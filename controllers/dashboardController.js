const Order = require('../models/Order');
const Product = require('../models/Product');
const { querySchema } = require('../dto');

/**
 * Get dashboard statistics
 * GET /api/dashboard
 */
const getDashboardStats = async (req, res) => {
  try {
    // Validate query parameters
    const { error, value } = querySchema.validate(req.query, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { lowStockThreshold = 10 } = req.query; // Default low stock threshold

    // Get total number of orders
    const totalOrders = await Order.countDocuments();

    // Get total number of product types
    const totalProductTypes = await Product.countDocuments();

    // Get orders by status
    const ordersByStatus = await Order.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Get low stock products
    const lowStockProducts = await Product.find({
      $or: [
        { totalQuantity: { $lte: lowStockThreshold } },
        { 'colors.quantity': { $lte: lowStockThreshold } }
      ]
    }).select('productName code totalQuantity colors specificationAndPacking');

    // Process low stock products to show which colors are low
    const processedLowStockProducts = lowStockProducts.map(product => {
      const lowStockColors = product.colors.filter(color => color.quantity <= lowStockThreshold);
      
      return {
        _id: product._id,
        productName: product.productName,
        code: product.code,
        totalQuantity: product.totalQuantity,
        specificationAndPacking: product.specificationAndPacking,
        isLowStock: product.totalQuantity <= lowStockThreshold,
        lowStockColors: lowStockColors.map(color => ({
          color: color.color,
          code: color.code,
          quantity: color.quantity,
          isLowStock: true
        })),
        allColors: product.colors.map(color => ({
          color: color.color,
          code: color.code,
          quantity: color.quantity,
          isLowStock: color.quantity <= lowStockThreshold
        }))
      };
    });

    // Get recent orders (last 10)
    const recentOrders = await Order.find()
      .populate('items.productId', 'productName code')
      .sort({ createdAt: -1 })
      .limit(10)
      .select('orderNumber customerName customerPhone totalAmount status createdAt');

    // Calculate total revenue
    const revenueStats = await Order.aggregate([
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          averageOrderValue: { $avg: '$totalAmount' }
        }
      }
    ]);

    // Get monthly order trends (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyOrderTrends = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          orderCount: { $sum: 1 },
          totalRevenue: { $sum: '$totalAmount' }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    // Get top selling products (by quantity ordered)
    const topSellingProducts = await Order.aggregate([
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.productId',
          productName: { $first: '$items.productName' },
          productCode: { $first: '$items.productCode' },
          totalQuantitySold: { $sum: '$items.quantity' },
          totalRevenue: { $sum: '$items.totalPrice' },
          orderCount: { $sum: 1 }
        }
      },
      { $sort: { totalQuantitySold: -1 } },
      { $limit: 10 }
    ]);

    res.status(200).json({
      success: true,
      data: {
        summary: {
          totalOrders,
          totalProductTypes,
          lowStockProductsCount: processedLowStockProducts.length,
          totalRevenue: revenueStats[0]?.totalRevenue || 0,
          averageOrderValue: revenueStats[0]?.averageOrderValue || 0
        },
        ordersByStatus: ordersByStatus.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        lowStockProducts: processedLowStockProducts,
        recentOrders,
        monthlyTrends: monthlyOrderTrends,
        topSellingProducts,
        settings: {
          lowStockThreshold
        }
      }
    });
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get low stock products only
 * GET /api/dashboard/low-stock
 */
const getLowStockProducts = async (req, res) => {
  try {
    const { lowStockThreshold = 10 } = req.query;

    // Validate threshold
    const threshold = parseInt(lowStockThreshold);
    if (isNaN(threshold) || threshold < 0) {
      return res.status(400).json({
        success: false,
        message: 'Low stock threshold must be a non-negative number'
      });
    }

    // Get low stock products
    const lowStockProducts = await Product.find({
      $or: [
        { totalQuantity: { $lte: threshold } },
        { 'colors.quantity': { $lte: threshold } }
      ]
    }).select('productName code totalQuantity colors specificationAndPacking productImageUrl');

    // Process low stock products
    const processedLowStockProducts = lowStockProducts.map(product => {
      const lowStockColors = product.colors.filter(color => color.quantity <= threshold);
      
      return {
        _id: product._id,
        productName: product.productName,
        code: product.code,
        totalQuantity: product.totalQuantity,
        specificationAndPacking: product.specificationAndPacking,
        productImageUrl: product.productImageUrl,
        isLowStock: product.totalQuantity <= threshold,
        lowStockColors: lowStockColors.map(color => ({
          color: color.color,
          code: color.code,
          quantity: color.quantity
        })),
        allColors: product.colors.map(color => ({
          color: color.color,
          code: color.code,
          quantity: color.quantity,
          isLowStock: color.quantity <= threshold
        }))
      };
    });

    res.status(200).json({
      success: true,
      data: {
        lowStockProducts: processedLowStockProducts,
        count: processedLowStockProducts.length,
        threshold
      }
    });
  } catch (error) {
    console.error('Error getting low stock products:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getDashboardStats,
  getLowStockProducts
};
