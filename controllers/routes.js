const express = require('express');
const { verifyToken } = require('../middleware/auth');
const { uploadSingleImage } = require('../middleware/upload');
const {
  addProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct
} = require('./productController');

// Import DTO schemas
const {
  addProductSchema,
  updateProductSchema,
  querySchema,
  idSchema
} = require('../dto');

const router = express.Router();

// Validation middleware
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, { abortEarly: false });
    console.log('validateBody error', error);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }
    req.body = value;
    next();
  };
};

const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }
    req.query = value;
    next();
  };
};

const validateParams = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.params, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }
    req.params = value;
    next();
  };
};

// Health check route (no auth required)
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Apply authentication middleware to all API routes
router.use('/api', verifyToken);

// Product routes
router.post('/api/products',
  uploadSingleImage('productImage'),
  validateBody(addProductSchema),
  addProduct
);

router.get('/api/products',
  validateQuery(querySchema),
  getAllProducts
);

router.get('/api/products/:id',
  validateParams(idSchema),
  getProductById
);

router.put('/api/products/:id',
  validateParams(idSchema),
  uploadSingleImage('productImage'),
  validateBody(updateProductSchema),
  updateProduct
);

router.delete('/api/products/:id',
  validateParams(idSchema),
  deleteProduct
);

module.exports = router;
