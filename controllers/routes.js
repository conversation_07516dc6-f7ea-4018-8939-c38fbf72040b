const express = require('express');
const { verifyToken } = require('../middleware/auth');
const { uploadSingleImage } = require('../middleware/upload');
const {
  addProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct,
  validateAddProduct,
  validateUpdateProduct,
  validateQuery,
  validateParams
} = require('./productController');

const router = express.Router();

// Health check route (no auth required)
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Apply authentication middleware to all API routes
router.use('/api', verifyToken);

// Product routes
router.post('/api/products',
  uploadSingleImage('productImage'),
  validateAddProduct,
  addProduct
);

router.get('/api/products',
  validateQuery,
  getAllProducts
);

router.get('/api/products/:id',
  validateParams,
  getProductById
);

router.put('/api/products/:id',
  validateParams,
  uploadSingleImage('productImage'),
  validateUpdateProduct,
  updateProduct
);

router.delete('/api/products/:id',
  validateParams,
  deleteProduct
);

module.exports = router;
