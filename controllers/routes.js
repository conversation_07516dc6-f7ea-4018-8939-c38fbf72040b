const express = require('express');
const { verifyToken } = require('../middleware/auth');
const { uploadSingleImage } = require('../middleware/upload');
const {
  addProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct
} = require('./productController');

const router = express.Router();

// Health check route (no auth required)
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Apply authentication middleware to all API routes
router.use('/api', verifyToken);

// Product routes
router.post('/api/products',
  uploadSingleImage('productImage'),
  addProduct
);

router.get('/api/products',
  getAllProducts
);

router.get('/api/products/:id',
  getProductById
);

router.put('/api/products/:id',
  uploadSingleImage('productImage'),
  updateProduct
);

router.delete('/api/products/:id',
  deleteProduct
);

module.exports = router;
