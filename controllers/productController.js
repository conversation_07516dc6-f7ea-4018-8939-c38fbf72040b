const Product = require('../models/Product');
const {
  addProductSchema,
  updateProductSchema,
  querySchema,
  idSchema
} = require('../dto');

/**
 * Add a new product
 * POST /api/products
 */
const addProduct = async (req, res) => {
  try {
    // Validate request body
    const body = {...req.body, colors:JSON.parse(req.body?.colors)};
    const { error, value } = addProductSchema.validate(body, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { productName, code, specificationAndPacking, colors } = value;

    // Check if product code already exists
    const existingProduct = await Product.findOne({ code });
    if (existingProduct) {
      return res.status(400).json({
        success: false,
        message: 'Product with this code already exists'
      });
    }

    // Get image URL from upload middleware
    const productImageUrl = req.uploadedImageUrl;
    if (!productImageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Product image is required'
      });
    }

    // Create new product
    const newProduct = new Product({
      productName,
      code,
      specificationAndPacking,
      productImageUrl,
      colors,
      createdBy: req.user.uid,
      updatedBy: req.user.uid
    });

    const savedProduct = await newProduct.save();

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: savedProduct
    });
  } catch (error) {
    console.error('Error adding product:', error);

    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: Object.values(error.errors).map(err => err.message)
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get all products
 * GET /api/products
 */
const getAllProducts = async (req, res) => {
  try {
    // Validate query parameters
    console.log('request.query:', req.query)
    const { error, value } = querySchema.validate(req.query, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { page, limit, search } = value;

    // Build search query
    const searchQuery = search ? {
      $or: [
        { productName: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } }
      ]
    } : {};

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get products with pagination
    const products = await Product.find(searchQuery)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalProducts = await Product.countDocuments(searchQuery);
    const totalPages = Math.ceil(totalProducts / limit);

    res.status(200).json({
      success: true,
      data: products,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalProducts,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Error getting products:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get a single product by ID
 * GET /api/products/:id
 */
const getProductById = async (req, res) => {
  try {
    // Validate ID parameter
    const { error, value } = idSchema.validate(req.params, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { id } = value;

    const product = await Product.findById(id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Error getting product:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Update a product
 * PUT /api/products/:id
 */
const updateProduct = async (req, res) => {
  try {
    // Validate ID parameter
    const { error: paramError, value: paramValue } = idSchema.validate(req.params, { abortEarly: false });
    if (paramError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: paramError.details.map(detail => detail.message)
      });
    }

    // Validate request body
    const { error: bodyError, value: bodyValue } = updateProductSchema.validate(req.body, { abortEarly: false });
    if (bodyError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: bodyError.details.map(detail => detail.message)
      });
    }

    const { id } = paramValue;
    const { productName, code, specificationAndPacking, colors } = bodyValue;

    // Check if product exists
    const existingProduct = await Product.findById(id);
    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // If code is being updated, check for duplicates
    if (code && code !== existingProduct.code) {
      const duplicateProduct = await Product.findOne({ code, _id: { $ne: id } });
      if (duplicateProduct) {
        return res.status(400).json({
          success: false,
          message: 'Product with this code already exists'
        });
      }
    }

    // Prepare update object
    const updateData = {
      updatedBy: req.user.uid
    };

    if (productName) updateData.productName = productName;
    if (code) updateData.code = code;
    if (specificationAndPacking) updateData.specificationAndPacking = specificationAndPacking;
    if (colors) updateData.colors = colors;

    // If new image uploaded, use it
    if (req.uploadedImageUrl) {
      updateData.productImageUrl = req.uploadedImageUrl;
    }

    const updatedProduct = await Product.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct
    });
  } catch (error) {
    console.error('Error updating product:', error);

    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: Object.values(error.errors).map(err => err.message)
      });
    }

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Delete a product
 * DELETE /api/products/:id
 */
const deleteProduct = async (req, res) => {
  try {
    // Validate ID parameter
    const { error, value } = idSchema.validate(req.params, { abortEarly: false });
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { id } = value;

    const product = await Product.findById(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    await Product.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting product:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  addProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct
};
