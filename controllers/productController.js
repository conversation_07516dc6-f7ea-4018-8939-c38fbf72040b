const Product = require('../models/Product');

/**
 * Add a new product
 * POST /api/products
 */
const addProduct = async (req, res) => {
  try {
    const { productName, code, specificationAndPacking, colors } = req.body;

    // Validate required fields
    if (!productName || !code || !specificationAndPacking || !colors) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required: productName, code, specificationAndPacking, colors'
      });
    }

    // Validate colors array
    if (!Array.isArray(colors) || colors.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Colors must be a non-empty array'
      });
    }

    // Validate each color object
    for (const color of colors) {
      if (!color.color || typeof color.quantity !== 'number' || color.quantity < 0) {
        return res.status(400).json({
          success: false,
          message: 'Each color must have a valid color name and non-negative quantity'
        });
      }
    }

    // Check if product code already exists
    const existingProduct = await Product.findOne({ code });
    if (existingProduct) {
      return res.status(400).json({
        success: false,
        message: 'Product with this code already exists'
      });
    }

    // Get image URL from upload middleware
    const productImageUrl = req.uploadedImageUrl;
    if (!productImageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Product image is required'
      });
    }

    // Create new product
    const newProduct = new Product({
      productName,
      code,
      specificationAndPacking,
      productImageUrl,
      colors,
      createdBy: req.user.uid,
      updatedBy: req.user.uid
    });

    const savedProduct = await newProduct.save();

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: savedProduct
    });
  } catch (error) {
    console.error('Error adding product:', error);
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: Object.values(error.errors).map(err => err.message)
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get all products
 * GET /api/products
 */
const getAllProducts = async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    
    // Build search query
    const searchQuery = search ? {
      $or: [
        { productName: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } }
      ]
    } : {};

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get products with pagination
    const products = await Product.find(searchQuery)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalProducts = await Product.countDocuments(searchQuery);
    const totalPages = Math.ceil(totalProducts / parseInt(limit));

    res.status(200).json({
      success: true,
      data: products,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalProducts,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Error getting products:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get a single product by ID
 * GET /api/products/:id
 */
const getProductById = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findById(id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Error getting product:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Update a product
 * PUT /api/products/:id
 */
const updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const { productName, code, specificationAndPacking, colors } = req.body;

    // Check if product exists
    const existingProduct = await Product.findById(id);
    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // If code is being updated, check for duplicates
    if (code && code !== existingProduct.code) {
      const duplicateProduct = await Product.findOne({ code, _id: { $ne: id } });
      if (duplicateProduct) {
        return res.status(400).json({
          success: false,
          message: 'Product with this code already exists'
        });
      }
    }

    // Validate colors if provided
    if (colors) {
      if (!Array.isArray(colors) || colors.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Colors must be a non-empty array'
        });
      }

      for (const color of colors) {
        if (!color.color || typeof color.quantity !== 'number' || color.quantity < 0) {
          return res.status(400).json({
            success: false,
            message: 'Each color must have a valid color name and non-negative quantity'
          });
        }
      }
    }

    // Prepare update object
    const updateData = {
      updatedBy: req.user.uid
    };

    if (productName) updateData.productName = productName;
    if (code) updateData.code = code;
    if (specificationAndPacking) updateData.specificationAndPacking = specificationAndPacking;
    if (colors) updateData.colors = colors;

    // If new image uploaded, use it
    if (req.uploadedImageUrl) {
      updateData.productImageUrl = req.uploadedImageUrl;
    }

    const updatedProduct = await Product.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct
    });
  } catch (error) {
    console.error('Error updating product:', error);

    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: Object.values(error.errors).map(err => err.message)
      });
    }

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Delete a product
 * DELETE /api/products/:id
 */
const deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findById(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    await Product.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting product:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  addProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct
};
