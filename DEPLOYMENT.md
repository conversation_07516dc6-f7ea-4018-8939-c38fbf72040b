# Google Cloud Deployment Guide

This guide helps you deploy the Will Global Trading Backend to Google Cloud App Engine with the most cost-effective configuration.

## Prerequisites

1. **Google Cloud Account**: Create a free account at [cloud.google.com](https://cloud.google.com)
2. **Google Cloud CLI**: Install from [cloud.google.com/sdk](https://cloud.google.com/sdk/docs/install)
3. **Node.js**: Ensure you have Node.js 20+ installed

## Cost Optimization Features

Our `app.yaml` configuration is optimized for minimal costs:

- **F1 Instance Class**: Cheapest available instance type
- **Scale to Zero**: Automatically scales down to 0 instances when no traffic
- **Minimal Resources**: 600MB memory, 1 CPU, 10GB disk
- **Smart Scaling**: Only scales up at 80% CPU/throughput utilization

## Deployment Steps

### 1. Setup Google Cloud Project

```bash
# Install Google Cloud CLI (if not already installed)
# Follow instructions at: https://cloud.google.com/sdk/docs/install

# Authenticate with Google Cloud
gcloud auth login

# Create a new project (or use existing)
gcloud projects create your-project-id --name="Will Global Trading"

# Set the project
gcloud config set project your-project-id

# Enable required APIs
gcloud services enable appengine.googleapis.com
gcloud services enable cloudbuild.googleapis.com
```

### 2. Configure Environment Variables

Update the `app.yaml` file with your environment variables:

```yaml
env_variables:
  NODE_ENV: production
  PORT: 8080
  MONGODB_URI: your-mongodb-connection-string
  FIREBASE_PROJECT_ID: your-firebase-project-id
  FIREBASE_PRIVATE_KEY_ID: your-private-key-id
  FIREBASE_PRIVATE_KEY: "-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
  FIREBASE_CLIENT_EMAIL: your-client-email
  FIREBASE_CLIENT_ID: your-client-id
  FIREBASE_STORAGE_BUCKET: your-storage-bucket.appspot.com
```

### 3. Deploy Using Script

```bash
# Make the deployment script executable
chmod +x deploy.sh

# Run the deployment script
./deploy.sh
```

### 4. Manual Deployment (Alternative)

```bash
# Install production dependencies
npm install --production

# Deploy to App Engine
gcloud app deploy app.yaml

# View your deployed app
gcloud app browse
```

## Post-Deployment

### Monitor Your Application

```bash
# View logs
gcloud app logs tail -s default

# Check app status
gcloud app describe

# View instances
gcloud app instances list
```

### Test Your API

```bash
# Health check
curl https://your-app-id.appspot.com/health

# Test products endpoint (requires authentication)
curl -H "Authorization: Bearer YOUR_FIREBASE_TOKEN" \
     https://your-app-id.appspot.com/api/products
```

## Cost Management

### Free Tier Limits

Google Cloud App Engine provides generous free tier limits:
- **28 instance hours per day** (F1/B1 instances)
- **1GB outbound data per day**
- **5GB Cloud Storage**

### Cost Optimization Tips

1. **Monitor Usage**: Set up billing alerts in Google Cloud Console
2. **Scale Down**: App automatically scales to 0 instances when idle
3. **Resource Limits**: F1 instances are the cheapest option
4. **Database**: Use MongoDB Atlas free tier or Google Cloud Firestore
5. **Storage**: Firebase Storage has generous free tier

### Estimated Monthly Costs

With our configuration and typical usage:
- **Light usage** (< 1000 requests/day): **FREE** (within free tier)
- **Medium usage** (10,000 requests/day): **$5-15/month**
- **Heavy usage** (100,000 requests/day): **$30-60/month**

## Troubleshooting

### Common Issues

1. **Deployment Fails**
   ```bash
   # Check if APIs are enabled
   gcloud services list --enabled
   
   # Ensure you have the correct permissions
   gcloud auth list
   ```

2. **App Won't Start**
   ```bash
   # Check logs for errors
   gcloud app logs tail -s default
   
   # Verify environment variables
   gcloud app describe
   ```

3. **Database Connection Issues**
   - Ensure MongoDB connection string is correct
   - Check if MongoDB Atlas allows connections from Google Cloud IPs
   - Verify Firebase credentials are properly formatted

### Support

- **Google Cloud Documentation**: [cloud.google.com/appengine/docs](https://cloud.google.com/appengine/docs)
- **App Engine Pricing**: [cloud.google.com/appengine/pricing](https://cloud.google.com/appengine/pricing)
- **Firebase Documentation**: [firebase.google.com/docs](https://firebase.google.com/docs)

## Security Notes

- Never commit `.env` files or credentials to version control
- Use Google Cloud Secret Manager for sensitive data in production
- Enable HTTPS (automatically provided by App Engine)
- Configure CORS properly for your frontend domain
- Regularly rotate Firebase service account keys
